import 'package:json_annotation/json_annotation.dart';

part 'update_user_request_model.g.dart';

@JsonSerializable()
class UpdateUserRequestModel {
  // @J<PERSON><PERSON><PERSON>(name: 'first_name')
  // final String? firstName;

  // @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  // final String? lastName;

  @J<PERSON><PERSON>ey(name: 'full_name')
  final String? fullName;

  final String? avatar;
  // final String? address;
  // final String? email;

  // @<PERSON><PERSON><PERSON><PERSON>(name: 'phone_number')
  // final String? phoneNumber;

  final String? bio;

  // @<PERSON><PERSON><PERSON><PERSON>(name: 'more_info')
  // final Map<String, dynamic>? moreInfo;

  const UpdateUserRequestModel({
    // this.firstName,
    // this.lastName,
    this.fullName,
    this.avatar,
    // this.address,
    // this.email,
    // this.phoneNumber,
    this.bio,
    // this.moreInfo,
  });

  factory UpdateUserRequestModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateUserRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateUserRequestModelToJson(this);

  UpdateUserRequestModel copyWith({
    String? firstName,
    String? lastName,
    String? fullName,
    String? avatar,
    String? address,
    String? email,
    String? phoneNumber,
    String? bio,
    Map<String, dynamic>? moreInfo,
  }) {
    return UpdateUserRequestModel(
      fullName: fullName ?? this.fullName,
      avatar: avatar ?? this.avatar,

      bio: bio ?? this.bio,
    );
  }
}
