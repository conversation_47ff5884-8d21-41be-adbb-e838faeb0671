import 'package:toii_social/core/service/user_service.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/follower/follower_model.dart';
import 'package:toii_social/model/user/update_user_request_model.dart';
import 'package:toii_social/model/user/user_model.dart';

abstract class UserRepository {
  Future<BaseResponse<UserModel>> getUserStats(String userId);

  Future<BaseResponse<FollowerListModel>> getFollowers();
  Future<BaseResponse<FollowingListModel>> getFollowing();
  Future<BaseResponse<FollowingListModel>> getUserFollowing(String userId);
  Future<BaseResponse<UserModel>> updateUser(
    String userId,
    UpdateUserRequestModel request,
  );
}

class UserRepositoryImpl extends UserRepository {
  final UserService userService;

  UserRepositoryImpl({required this.userService});

  @override
  Future<BaseResponse<UserModel>> getUserStats(String userId) async {
    try {
      return await userService.getUserStats(userId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<FollowerListModel>> getFollowers() {
    return userService.getFollowers();
  }

  @override
  Future<BaseResponse<FollowingListModel>> getFollowing() {
    return userService.getFollowing();
  }

  @override
  Future<BaseResponse<FollowingListModel>> getUserFollowing(String userId) {
    return userService.getUserFollowing(userId);
  }

  @override
  Future<BaseResponse<UserModel>> updateUser(
    String userId,
    UpdateUserRequestModel request,
  ) async {
    try {
      return await userService.updateUser(userId, request);
    } catch (e) {
      rethrow;
    }
  }
}
