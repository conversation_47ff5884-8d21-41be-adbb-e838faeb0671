import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/user/edit_profile/edit_profile_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class EditProfileScreen extends StatefulWidget {
  final UserModel? user;

  const EditProfileScreen({super.key, this.user});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  late EditProfileCubit _editProfileCubit;
  late TextEditingController _displayNameController;
  late TextEditingController _bioController;

  @override
  void initState() {
    super.initState();
    _editProfileCubit = EditProfileCubit();
    _displayNameController = TextEditingController();
    _bioController = TextEditingController();

    if (widget.user != null) {
      _editProfileCubit.initializeProfile(widget.user!);
      _displayNameController.text =
          widget.user!.fullName ?? widget.user!.firstName ?? '';
      _bioController.text = widget.user!.moreInfo?['bio'] as String? ?? '';
    }

    // Add listener to update character count
    _bioController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _bioController.dispose();
    _editProfileCubit.resetState();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _editProfileCubit,
      child: BlocConsumer<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Profile updated successfully')),
            );
            context.pop();
          } else if (state.status.isFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'Failed to update profile'),
              ),
            );
          }
        },
        builder: (context, state) {
          return Scaffold(
            backgroundColor: const Color(
              0xFF0F0F0F,
            ), // Keep as hardcoded for dark background
            resizeToAvoidBottomInset: true, // Handle keyboard properly
            body: Stack(
              children: [
                // Background image
                Positioned.fill(
                  child: Assets.images.defaultBackground.image(
                    fit: BoxFit.cover,
                  ),
                ),
                // Gradient overlays
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          const Color(0xFF0F0F0F).withValues(alpha: 0.8),
                        ],
                      ),
                    ),
                  ),
                ),
                // Main content
                GestureDetector(
                  onTap: () {
                    // Hide keyboard when tapping outside
                    FocusScope.of(context).unfocus();
                  },
                  child: SafeArea(
                    child: Column(
                      children: [
                        _buildAppBar(context, state),
                        Expanded(
                          child: SingleChildScrollView(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: ConstrainedBox(
                              constraints: BoxConstraints(
                                minHeight:
                                    MediaQuery.of(context).size.height -
                                    MediaQuery.of(context).padding.top -
                                    MediaQuery.of(context).padding.bottom -
                                    100, // Subtract app bar height
                              ),
                              child: IntrinsicHeight(
                                child: Column(
                                  children: [
                                    const Spacer(), // This will push content to bottom
                                    Stack(
                                      clipBehavior: Clip.none,
                                      children: [
                                        Column(
                                          children: [
                                            const SizedBox(
                                              height: 72,
                                            ), // Space for avatar
                                            _buildFormCard(state),
                                          ],
                                        ),
                                        // Avatar positioned to overlap the card
                                        Positioned(
                                          top: 36,
                                          left: 0,
                                          right: 0,
                                          child: _buildAvatarSection(state),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAppBar(BuildContext context, EditProfileState state) {
    final themeData = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Close button
          Container(
            decoration: BoxDecoration(
              color: themeData.white200,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () => context.pop(),
                child: Container(
                  width: 32,
                  height: 32,
                  padding: const EdgeInsets.all(6),
                  child: Icon(
                    Icons.close,
                    color: themeData.textContrast,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
          // Title
          const SizedBox(width: 16),
          Text(
            'Edit profile',
            style: titleLarge.copyWith(
              color: themeData.textContrast,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const Spacer(),
          // Save button
          Container(
            decoration: BoxDecoration(
              color:
                  state.hasChanges && !state.status.isLoading
                      ? themeData.primaryGreen500
                      : themeData.neutral100,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(24),
                onTap:
                    state.hasChanges && !state.status.isLoading
                        ? () => _saveProfile()
                        : null,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child:
                      state.status.isLoading
                          ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: themeData.textContrast,
                            ),
                          )
                          : Text(
                            'Save',
                            style: titleMedium.copyWith(
                              color:
                                  state.hasChanges && !state.status.isLoading
                                      ? themeData.textContrast
                                      : themeData.neutral300,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormCard(EditProfileState state) {
    final themeData = Theme.of(context);
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      padding: const EdgeInsets.only(top: 60, bottom: 20, left: 16, right: 16),
      decoration: BoxDecoration(
        color: themeData.white900,
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: themeData.black50,
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: _buildFormFields(state),
    );
  }

  Widget _buildAvatarSection(EditProfileState state) {
    final themeData = Theme.of(context);
    return Center(
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Avatar container with border matching Figma design
          Container(
            width: 64,
            height: 64,

            decoration: BoxDecoration(
              color: themeData.black400,
              borderRadius: BorderRadius.circular(32),
              border: Border.all(color: themeData.neutral300, width: 1),
            ),
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: themeData.neutral50, width: 1),
                shape: BoxShape.circle,
              ),
              child: CircleAvatar(
                radius: 32,
                backgroundImage: NetworkImage(
                  state.avatar ??
                      widget.user?.avatar ??
                      'https://randomuser.me/api/portraits/women/47.jpg',
                ),
              ),
            ),
          ),
          // Camera icon overlay positioned at bottom right
          Positioned(
            bottom: 0,
            right: 0,
            child: GestureDetector(
              onTap: _selectAvatar,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: themeData.white900,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.camera_alt,
                  color: themeData.textPrimary,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields(EditProfileState state) {
    final themeData = Theme.of(context);
    return Column(
      children: [
        // Display Name Field
        _buildLabeledField(
          label: 'Display Name',
          child: TTextField(
            showBorder: true,
            fillColor: themeData.white900,
            textController: _displayNameController,
            hintText: widget.user?.fullName ?? widget.user?.firstName ?? '',
            onChanged: (value) => _editProfileCubit.updateDisplayName(value),
            textStyle: bodyMedium.copyWith(color: themeData.textPrimary),
            hintStyle: bodyMedium.copyWith(color: themeData.textSecondary),
          ),
        ),
        const SizedBox(height: 6),

        // Username Field (Disabled)
        _buildLabeledField(
          label: 'Username',
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 14),
            decoration: BoxDecoration(
              color: themeData.neutral100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.user?.username ?? '',
                    style: bodyMedium.copyWith(color: themeData.textSecondary),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 6),

        // Bio Field
        _buildBioField(state),
      ],
    );
  }

  Widget _buildLabeledField({required String label, required Widget child}) {
    final themeData = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 5, bottom: 6),
          child: Text(
            label,
            style: titleMedium.copyWith(
              color: themeData.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        child,
      ],
    );
  }

  Widget _buildBioField(EditProfileState state) {
    final themeData = Theme.of(context);
    return _buildLabeledField(
      label: 'Bio',
      child: Column(
        children: [
          TTextField(
            showBorder: true,
            fillColor: themeData.white900,
            textController: _bioController,
            hintText: 'Write something short about you...',
            maxLines: 4,
            onChanged: (value) => _editProfileCubit.updateBio(value),
            textStyle: bodyMedium.copyWith(color: themeData.textPrimary),
            hintStyle: bodyMedium.copyWith(color: themeData.textSecondary),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 8, right: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  '${_bioController.text.length}/120',
                  style: labelSmall.copyWith(color: themeData.textSecondary),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _selectAvatar() {
    // TODO: Implement avatar selection (camera/gallery)
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Avatar selection coming soon')),
    );
  }

  void _saveProfile() {
    if (widget.user?.id != null) {
      _editProfileCubit.saveProfile(widget.user!.id);
    }
  }
}
